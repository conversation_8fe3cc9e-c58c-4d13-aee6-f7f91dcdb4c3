#!/usr/bin/env python3
"""
趋势线策略分析运行脚本
对应Oracle gp_Trend_pkg.Trend_main的调用
"""

import sys
import os
import argparse
import logging
from datetime import datetime

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(current_dir, '..', '..'))

from pkg.trend.main import TrendAnalysisMain
from pkg.trend.config import DEBUG_CONFIG

def setup_logging(debug: bool = False):
    """设置日志"""
    level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'trend_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
            logging.StreamHandler()
        ]
    )

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='趋势线策略分析')
    parser.add_argument('--subject', default='SZ_SH_TREND', help='邮件主题')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--test-stock', help='测试单只股票（调试用）')
    parser.add_argument('--detailed-log', action='store_true', help='启用详细日志')
    
    args = parser.parse_args()
    
    # 设置调试配置
    if args.debug:
        DEBUG_CONFIG['enable_detailed_logging'] = True
        DEBUG_CONFIG['print_debug_info'] = True
    
    if args.test_stock:
        DEBUG_CONFIG['test_single_stock'] = args.test_stock
    
    if args.detailed_log:
        DEBUG_CONFIG['enable_detailed_logging'] = True
    
    # 设置日志
    setup_logging(args.debug or args.detailed_log)
    
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("=" * 60)
        logger.info("趋势线策略分析开始")
        logger.info(f"邮件主题: {args.subject}")
        logger.info(f"调试模式: {args.debug}")
        logger.info(f"测试股票: {args.test_stock or '全部'}")
        logger.info("=" * 60)
        
        # 创建分析器并运行
        analyzer = TrendAnalysisMain()
        analyzer.run_analysis(args.subject)
        
        logger.info("=" * 60)
        logger.info("趋势线策略分析完成")
        logger.info("=" * 60)
        
    except KeyboardInterrupt:
        logger.info("用户中断程序执行")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
