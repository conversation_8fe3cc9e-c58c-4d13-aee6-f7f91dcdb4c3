"""
日志配置文件
用于配置程序的日志输出格式和级别
"""

import os

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'logs/stock_analysis.log',
    'max_size': 10 * 1024 * 1024,    # 10MB
    'backup_count': 5,
    'encoding': 'utf-8',
}

# 调试日志配置
DEBUG_LOGGING_CONFIG = {
    'level': 'DEBUG',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    'file': 'logs/pattern_debug.log',
    'max_size': 50 * 1024 * 1024,    # 50MB
    'backup_count': 3,
    'encoding': 'utf-8',
} 