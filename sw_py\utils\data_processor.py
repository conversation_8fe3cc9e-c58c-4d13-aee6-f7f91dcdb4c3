"""
数据处理工具类
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple, Optional
import logging
import os
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders

class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def convert_daily_to_weekly(self, daily_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """将日线数据转换为周线数据 - 修复周编号算法与Oracle一致"""
        if not daily_data:
            return []
        
        # 转换为DataFrame便于处理
        df = pd.DataFrame(daily_data)
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        df = df.sort_values('trade_date')
        
        # 使用与Oracle一致的周编号算法
        df['week_key'] = df['trade_date'].apply(self.get_oracle_compatible_week_num)
        
        weekly_data = []
        for week_key, group in df.groupby('week_key'):
            if len(group) == 0:
                continue
            
            # 计算周线OHLC - 使用当周最大交易日作为trade_date
            max_trade_date = group['trade_date'].max()  # 明确取当周最大交易日
            min_trade_date = group['trade_date'].min()  # 获取当周最小交易日
            
            weekly_record = {
                'stock_code': group.iloc[0]['stock_code'],
                'trade_date': max_trade_date.strftime('%Y-%m-%d'),    # 当周最大交易日期
                'week_num': week_key,  # 使用Oracle兼容的周编号
                'open_price': float(group[group['trade_date'] == min_trade_date]['open_price'].iloc[0]),     # 最早交易日的开盘价
                'high_price': float(group['high_price'].max()),       # 周内最高价
                'close_price': float(group[group['trade_date'] == max_trade_date]['close_price'].iloc[0]),  # 最晚交易日的收盘价
                'low_price': float(group['low_price'].min()),         # 周内最低价
                'jy_quantity': int(group['volume'].sum()),            # Oracle字段名：jy_quantity
                'jy_amount': float(group['amount'].sum())             # Oracle字段名：jy_amount
            }
            weekly_data.append(weekly_record)
        
        return weekly_data
    
    def get_oracle_compatible_week_num(self, trade_date: pd.Timestamp) -> str:
        """
        生成与Oracle GP_WEEK_INFO表兼容的周编号
        Oracle逻辑：按年度顺序编号，每年第1周从1开始
        """
        # 获取年份
        year = trade_date.year
        
        # 计算该日期是该年的第几周（从1开始）
        # 使用ISO周编号，但调整为与Oracle逻辑一致
        year_start = pd.Timestamp(f'{year}-01-01')
        
        # 找到该年第一个周一（Oracle通常以周一为一周开始）
        first_monday = year_start
        while first_monday.weekday() != 0:  # 0是周一
            first_monday += pd.Timedelta(days=1)
        
        # 如果1月1日在第一个周一之前，则属于上一年的最后一周
        if year_start < first_monday:
            if trade_date < first_monday:
                # 属于上一年，递归计算
                prev_year_end = pd.Timestamp(f'{year-1}-12-31')
                return self.get_oracle_compatible_week_num(prev_year_end)
        
        # 计算距离第一个周一的天数，然后计算周数
        days_diff = (trade_date - first_monday).days
        if days_diff < 0:
            # 属于上一年
            prev_year_end = pd.Timestamp(f'{year-1}-12-31')
            return self.get_oracle_compatible_week_num(prev_year_end)
        
        week_num = (days_diff // 7) + 1
        
        # 格式化为 YYYYWNN 格式（如：2024W01）
        return f"{year}W{week_num:02d}"
    
    def calculate_moving_average(self, data: List[Dict[str, Any]], 
                               avg_days: List[int], 
                               period_type: str = 'daily',
                               stock_code: str = None) -> List[Dict[str, Any]]:
        """计算移动平均值"""
        if not data:
            return []
        
        # 转换为DataFrame
        df = pd.DataFrame(data)
        df = df.sort_values('trade_date')
        
        avg_data = []
        
        for i, row in df.iterrows():
            current_date = row['trade_date']
            # 优先使用传入的stock_code，如果没有则尝试从数据中获取
            current_stock_code = stock_code or row.get('stock_code', 'UNKNOWN')
            
            for days in avg_days:
                # 获取前N天的数据
                start_idx = max(0, i - days + 1)
                period_data = df.iloc[start_idx:i+1]
                
                if len(period_data) >= min(days, len(df)):
                    avg_volume = period_data['volume'].mean()
                    avg_amount = period_data['amount'].mean()
                    
                    avg_record = {
                        'stock_code': current_stock_code,
                        'trade_date': current_date,
                        'period_type': period_type,
                        'avg_days': days,
                        'avg_volume': float(avg_volume),
                        'avg_amount': float(avg_amount)
                    }
                    avg_data.append(avg_record)
        
        return avg_data
    
    def add_row_numbers(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """为数据添加行号"""
        for i, record in enumerate(data):
            record['row_num'] = i + 1
        return data
    
    def calculate_turnover_rate(self, volume_data: List[float], 
                              start_idx: int, 
                              end_idx: int, 
                              total_shares: float = None) -> Tuple[float, float]:
        """计算换手率统计
        
        Args:
            volume_data: 成交量列表
            start_idx: 开始索引
            end_idx: 结束索引
            total_shares: 总股本（如果为None则使用相对换手率）
            
        Returns:
            (累计换手率, 平均换手率)
        """
        if start_idx >= end_idx or start_idx < 0 or end_idx > len(volume_data):
            return 0.0, 0.0
        
        period_volumes = volume_data[start_idx:end_idx]
        
        if total_shares and total_shares > 0:
            # 使用实际股本计算，避免除零错误
            turnover_rates = [vol / total_shares for vol in period_volumes if vol > 0 and total_shares > 0]
        else:
            # 使用相对换手率（假设基准为1），避免除零错误
            volume_sum = sum(period_volumes)
            turnover_rates = [vol / volume_sum if volume_sum > 0 else 0.0 
                            for vol in period_volumes]
        
        all_turnover_rate = sum(turnover_rates)
        avg_turnover_rate = all_turnover_rate / len(turnover_rates) if len(turnover_rates) > 0 else 0.0
        
        return all_turnover_rate, avg_turnover_rate
    
    def filter_valid_stocks(self, stock_list: List[Dict[str, Any]], 
                          exclude_st: bool = True) -> List[Dict[str, Any]]:
        """过滤有效的股票"""
        valid_stocks = []
        
        for stock in stock_list:
            stock_code = stock.get('gp_num', '')
            stock_name = stock.get('gp_name', '')
            
            # 过滤ST股票
            if exclude_st and ('ST' in stock_name or '*ST' in stock_name):
                continue
            
            # 过滤无效股票代码
            if not stock_code or len(stock_code) != 6 or not stock_code.isdigit():
                continue
            
            # 确定交易所
            if stock_code.startswith(('000', '001', '002', '003')):
                exchange = 'sz'  # 深交所
            elif stock_code.startswith(('600', '601', '603', '605')):
                exchange = 'sh'  # 上交所
            else:
                continue  # 其他代码跳过
            
            stock['jys_no'] = exchange
            valid_stocks.append(stock)
        
        return valid_stocks
    
    def convert_date_format(self, date_str: str, 
                          input_format: str = '%Y-%m-%d', 
                          output_format: str = '%Y-%m-%d') -> str:
        """转换日期格式"""
        try:
            date_obj = datetime.strptime(date_str, input_format)
            return date_obj.strftime(output_format)
        except ValueError as e:
            self.logger.warning(f"日期格式转换错误: {date_str}, {e}")
            return date_str
    
    def validate_numeric_data(self, data: List[Dict[str, Any]], 
                            required_fields: List[str]) -> List[Dict[str, Any]]:
        """验证和清理数值数据"""
        cleaned_data = []
        
        for record in data:
            valid_record = True
            cleaned_record = record.copy()
            
            for field in required_fields:
                value = record.get(field)
                
                if value is None:
                    valid_record = False
                    break
                
                # 转换为合适的数值类型
                try:
                    if field in ['volume']:
                        cleaned_record[field] = int(float(value))
                    elif field in ['open_price', 'high_price', 'close_price', 'low_price', 'amount']:
                        cleaned_record[field] = float(value)
                        
                        # 检查价格数据的合理性
                        if field.endswith('_price') and cleaned_record[field] <= 0:
                            valid_record = False
                            break
                            
                except (ValueError, TypeError):
                    valid_record = False
                    break
            
            if valid_record:
                cleaned_data.append(cleaned_record)
        
        return cleaned_data
    
    def chunk_data(self, data: List[Any], chunk_size: int) -> List[List[Any]]:
        """将数据分块处理"""
        chunks = []
        for i in range(0, len(data), chunk_size):
            chunks.append(data[i:i + chunk_size])
        return chunks
    
    def export_to_excel(self, results: List[Dict[str, Any]], export_config: Dict[str, Any]) -> str:
        """导出结果到Excel文件"""
        try:
            # 确保导出目录存在
            export_dir = export_config['export_dir']
            os.makedirs(export_dir, exist_ok=True)
            
            # 生成文件名（包含时间戳）
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{timestamp}_{export_config['excel_filename']}"
            filepath = os.path.join(export_dir, filename)
            
            if not results:
                # 创建空的Excel文件 - 使用Oracle包一致的字段名
                df = pd.DataFrame(columns=[
                    '股票编号', '股票名称', 'U型左侧高点周', 'U型左侧高点价格',
                    'U型右侧高点周', 'U型右侧高点价格', 'U型最低点周', 'U型最低点价格',
                    'V型最低点周', 'V型最低点价格', 'V型右侧高点周', 'V型右侧高点价格',
                    '最高点周九十天', '最高点价格九十天', '最低点周九十天', '内最低点价格九十天',
                    '最低点周三分之一', '最低点价格三分之一', '最低点周四分之一', '最低点价格四分之一',
                    'MA支撑点', '累计换手率', '平均换手率', '最近突破日', '突破日收盘价', '是否突破'
                ])
                df.to_excel(filepath, index=False, sheet_name='分析结果')
                self.logger.info(f"导出空结果到Excel: {filepath}")
                return filepath
            
            # 转换结果为DataFrame - 安全处理None值
            df_data = []
            for result in results:
                def safe_get(key, default_val):
                    """安全获取字段值，避免None值导致的错误"""
                    value = result.get(key)
                    if value is None:
                        return default_val
                    return value
                
                # 严格按照Oracle包第881-903行的字段映射
                df_data.append({
                    '股票编号': safe_get('stock_code', ''),                    # Oracle: gp_num
                    '股票名称': safe_get('stock_name', ''),                    # Oracle: gp_name
                    'U型左侧高点周': safe_get('u_left_date', ''),              # Oracle: u_ldate
                    'U型左侧高点价格': safe_get('u_left_price', 0) or 0,        # Oracle: u_lhight
                    'U型右侧高点周': safe_get('u_right_date', ''),              # Oracle: u_rdate
                    'U型右侧高点价格': safe_get('u_right_price', 0) or 0,        # Oracle: u_rhight
                    'U型最低点周': safe_get('u_lowest_date', ''),              # Oracle: u_lowest_date
                    'U型最低点价格': safe_get('u_lowest_price', 0) or 0,        # Oracle: u_lowest
                    'V型最低点周': safe_get('v_lowest_date', ''),              # Oracle: v_lowest_date
                    'V型最低点价格': safe_get('v_lowest_price', 0) or 0,        # Oracle: v_lowest
                    'V型右侧高点周': safe_get('v_right_date', ''),              # Oracle: v_rdate
                    'V型右侧高点价格': safe_get('v_right_price', 0) or 0,        # Oracle: v_rhight
                    '最高点周九十天': safe_get('success_date', ''),             # Oracle: sucess_date (注意拼写)
                    '最高点价格九十天': safe_get('success_price', 0) or 0,       # Oracle: sucess_price
                    '最低点周九十天': safe_get('lower_date', ''),               # Oracle: lower_date
                    '内最低点价格九十天': safe_get('lower_price', 0) or 0,       # Oracle: lower_price
                    '最低点周三分之一': safe_get('u_low1_date', ''),            # Oracle: u_low1_date
                    '最低点价格三分之一': safe_get('u_low1_price', 0) or 0,      # Oracle: u_low1_price
                    '最低点周四分之一': safe_get('u_low2_date', ''),            # Oracle: u_low2_date
                    '最低点价格四分之一': safe_get('u_low2_price', 0) or 0,      # Oracle: u_low2_price
                    'MA支撑点': safe_get('ma_num', 0) or 0,                   # Oracle: ma_num
                    '累计换手率': safe_get('all_turnover_rate', 0) or 0,       # Oracle: allqty_rate
                    '平均换手率': safe_get('avg_turnover_rate', 0) or 0,       # Oracle: avgqty_rate
                    '最近突破日': safe_get('breakthrough_date', ''),           # Oracle: ATTRIBUTE3
                    '突破日收盘价': safe_get('breakthrough_price', 0) or 0,     # Oracle: ATTRIBUTE1
                    '是否突破': '是' if safe_get('is_breakthrough', 0) == 1 else '否'  # Oracle: attribute2
                })
            
            df = pd.DataFrame(df_data)
            
            # 创建Excel写入器
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # 写入主要结果
                df.to_excel(writer, sheet_name='分析结果', index=False)
                
                # 添加统计汇总表 - 安全处理None值
                def safe_calc_u_pattern_gain():
                    """安全计算U型平均涨幅"""
                    u_gains = []
                    for r in results:
                        u_left = r.get('u_left_price')
                        u_right = r.get('u_right_price')
                        if u_left and u_right and u_left > 0:
                            gain = (u_right - u_left) / u_left * 100
                            u_gains.append(gain)
                    return f"{sum(u_gains) / len(u_gains):.2f}%" if u_gains else "无U型数据"
                
                def safe_calc_v_pattern_gain():
                    """安全计算V型平均涨幅"""
                    v_gains = []
                    for r in results:
                        v_right = r.get('v_right_price')
                        v_lowest = r.get('v_lowest_price')
                        if v_right and v_lowest and v_lowest > 0:
                            gain = (v_right - v_lowest) / v_lowest * 100
                            v_gains.append(gain)
                    return f"{sum(v_gains) / len(v_gains):.2f}%" if v_gains else "无V型数据"
                
                def safe_count_patterns():
                    """安全统计形态数量"""
                    count = 0
                    for r in results:
                        if (r.get('v_right_price') and r.get('v_right_price') > 0) or \
                           (r.get('u_right_price') and r.get('u_right_price') > 0):
                            count += 1
                    return count
                
                pattern_count = safe_count_patterns()
                summary_data = {
                    '指标': ['总分析股票数', '发现形态数', '成功率', '平均U型涨幅', '平均V型涨幅'],
                    '数值': [
                        len(results),
                        pattern_count,
                        f"{pattern_count / len(results) * 100:.2f}%" if results else "0%",
                        safe_calc_u_pattern_gain(),
                        safe_calc_v_pattern_gain()
                    ]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='统计汇总', index=False)
            
            self.logger.info(f"成功导出{len(results)}条结果到Excel: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"导出Excel失败: {str(e)}")
            raise
    
    def send_email_notification(self, excel_filepath: str, total_stocks: int, 
                                found_patterns: int, email_config: Dict[str, Any]) -> bool:
        """发送邮件通知"""
        return self.send_email(excel_filepath, email_config, 1, total_stocks, found_patterns)
    
    def send_email(self, excel_filepath: str, email_config: Dict[str, Any], 
                   batch_id: int, total_stocks: int, found_patterns: int) -> bool:
        """发送邮件"""
        try:
            # 检查是否启用邮件功能
            if not email_config.get('enable_email', False):
                self.logger.info("邮件发送功能已禁用")
                return False
            
            # 检查邮件配置
            if not email_config.get('username') or not email_config.get('password'):
                self.logger.warning("邮件配置不完整，跳过发送")
                return False
            
            if not email_config.get('to_emails'):
                self.logger.warning("未配置收件人，跳过发送")
                return False
            
            # 创建邮件对象
            msg = MIMEMultipart()
            msg['From'] = email_config['from_email'] or email_config['username']
            msg['To'] = ', '.join(email_config['to_emails'])
            msg['Subject'] = email_config['subject']
            
            # 邮件正文
            body = f"""
亲爱的用户：

U型V型股票技术分析已完成，具体结果如下：

分析批次：{batch_id}
分析股票总数：{total_stocks}
发现形态数量：{found_patterns}
成功率：{found_patterns/total_stocks*100:.2f}%
分析时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

详细结果请查看附件Excel文件。

此邮件由股票分析系统自动发送，请勿回复。

祝好！
股票分析系统
            """
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # 添加Excel附件
            if os.path.exists(excel_filepath):
                with open(excel_filepath, "rb") as attachment:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment.read())
                
                encoders.encode_base64(part)
                part.add_header(
                    'Content-Disposition',
                    f'attachment; filename= {os.path.basename(excel_filepath)}'
                )
                msg.attach(part)
            else:
                self.logger.warning(f"Excel文件不存在: {excel_filepath}")
            
            # 发送邮件
            if email_config.get('use_ssl', True):
                server = smtplib.SMTP_SSL(email_config['smtp_server'], email_config['smtp_port'])
            else:
                server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
                server.starttls()
            
            server.login(email_config['username'], email_config['password'])
            text = msg.as_string()
            server.sendmail(email_config['username'], email_config['to_emails'], text)
            server.quit()
            
            self.logger.info(f"邮件发送成功，收件人: {email_config['to_emails']}")
            return True
            
        except Exception as e:
            self.logger.error(f"邮件发送失败: {str(e)}")
            return False 