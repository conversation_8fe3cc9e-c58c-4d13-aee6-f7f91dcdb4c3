"""
数据库连接和数据模型
"""

import pymysql
import logging
from contextlib import contextmanager
from typing import List, Dict, Any
import time
import random

try:
    from sw_py.config.database_config import MYSQL_CONFIG
except ImportError:
    try:
        from config.database_config import MYSQL_CONFIG
    except ImportError:
        print("无法导入数据库配置，请检查 config/database_config.py 文件")
        MYSQL_CONFIG = {}

class DatabaseManager:
    """数据库管理器 - 优化并发和死锁处理"""
    
    def __init__(self):
        self.config = MYSQL_CONFIG.copy()
        # 优化连接配置以减少死锁
        self.config.update({
            'autocommit': False,
            'charset': 'utf8mb4',
            'connect_timeout': 60,
            'read_timeout': 60,
            'write_timeout': 60,
            'max_allowed_packet': 16 * 1024 * 1024,  # 16MB
        })
        self.logger = logging.getLogger(__name__)
        self.max_retries = 3  # 最大重试次数
        self.retry_delay = 0.1  # 重试延迟（秒）
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        connection = None
        try:
            connection = pymysql.connect(**self.config)
            connection.ping(reconnect=True)  # 确保连接有效
            yield connection
        except Exception as e:
            self.logger.error(f"数据库连接错误: {e}")
            if connection:
                connection.rollback()
            raise
        finally:
            if connection:
                connection.close()
    
    def execute_query(self, sql: str, params: tuple = None) -> List[Dict[str, Any]]:
        """执行查询SQL - 支持重试"""
        for attempt in range(self.max_retries + 1):
            try:
                with self.get_connection() as conn:
                    cursor = conn.cursor(pymysql.cursors.DictCursor)
                    cursor.execute(sql, params)
                    return cursor.fetchall()
            except pymysql.OperationalError as e:
                if e.args[0] in (1213, 1205) and attempt < self.max_retries:
                    wait_time = self.retry_delay * (2 ** attempt) + random.uniform(0, 0.1)
                    self.logger.warning(f"查询遇到死锁，第 {attempt + 1} 次重试，等待 {wait_time:.2f} 秒")
                    time.sleep(wait_time)
                    continue
                raise
            except Exception as e:
                self.logger.error(f"执行查询失败: {e}")
                raise
    
    def execute_update(self, sql: str, params: tuple = None) -> int:
        """执行更新SQL - 支持重试"""
        for attempt in range(self.max_retries + 1):
            try:
                with self.get_connection() as conn:
                    cursor = conn.cursor()
                    affected_rows = cursor.execute(sql, params)
                    conn.commit()
                    return affected_rows
            except pymysql.OperationalError as e:
                if e.args[0] in (1213, 1205) and attempt < self.max_retries:
                    wait_time = self.retry_delay * (2 ** attempt) + random.uniform(0, 0.1)
                    self.logger.warning(f"更新遇到死锁，第 {attempt + 1} 次重试，等待 {wait_time:.2f} 秒")
                    time.sleep(wait_time)
                    continue
                raise
            except Exception as e:
                self.logger.error(f"执行更新失败: {e}")
                raise
    
    def execute_batch(self, sql: str, params_list: List[tuple], batch_size: int = 1000) -> int:
        """批量执行SQL - 支持重试和分批处理"""
        if not params_list:
            return 0
        
        total_affected = 0
        
        # 分批处理大量数据
        for i in range(0, len(params_list), batch_size):
            batch_params = params_list[i:i + batch_size]
            
            for attempt in range(self.max_retries + 1):
                try:
                    with self.get_connection() as conn:
                        cursor = conn.cursor()
                        affected_rows = cursor.executemany(sql, batch_params)
                        conn.commit()
                        total_affected += affected_rows
                        break  # 成功，跳出重试循环
                except pymysql.OperationalError as e:
                    if e.args[0] in (1213, 1205) and attempt < self.max_retries:
                        wait_time = self.retry_delay * (2 ** attempt) + random.uniform(0, 0.1)
                        self.logger.warning(f"批量操作遇到死锁，第 {attempt + 1} 次重试，等待 {wait_time:.2f} 秒")
                        time.sleep(wait_time)
                        continue
                    raise
                except Exception as e:
                    self.logger.error(f"执行批量操作失败: {e}")
                    raise
        
        return total_affected

class StockDataModel:
    """股票数据模型"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.logger = logging.getLogger(__name__)
    
    def get_stock_list(self) -> List[Dict[str, Any]]:
        """获取股票列表"""
        sql = """
        SELECT code as gp_num, name as gp_name
        FROM stock_info_a_code_name
        WHERE code REGEXP '^[0-9]{6}$'
        ORDER BY code
        """
        return self.db.execute_query(sql)
    
    def get_stock_daily_data(self, stock_code: str, start_date: str = '2013-01-01') -> List[Dict[str, Any]]:
        """获取股票日线数据"""
        sql = """
        SELECT 
            `日期` as trade_date,
            `开盘` as open_price,
            `收盘` as close_price,
            `最高` as high_price,
            `最低` as low_price,
            `成交量` as volume,
            `成交额` as amount,
            `换手率` as turnover_rate,
            symbol as stock_code
        FROM stock_zh_a_hist_hfq
        WHERE symbol = %s AND `日期` >= %s
        ORDER BY `日期`
        """
        return self.db.execute_query(sql, (stock_code, start_date))
    
    def create_weekly_data_table(self):
        """创建周线数据表 - 严格对应Oracle GP_JY_W表结构"""
        sql = """
        CREATE TABLE IF NOT EXISTS stock_weekly_data (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            stock_code VARCHAR(10) NOT NULL COMMENT '股票代码 (对应Oracle gp_num)',
            trade_date DATE NOT NULL COMMENT '交易日期 (对应Oracle jy_date)',
            week_num VARCHAR(20) COMMENT '周编号 (对应Oracle week_num)',
            open_price DECIMAL(10,4) COMMENT '开盘价 (对应Oracle open_price)',
            high_price DECIMAL(10,4) COMMENT '最高价 (对应Oracle high_price)',
            close_price DECIMAL(10,4) COMMENT '收盘价 (对应Oracle close_price)',
            low_price DECIMAL(10,4) COMMENT '最低价 (对应Oracle low_price)',
            jy_quantity BIGINT COMMENT '交易量 (对应Oracle jy_quantity)',
            jy_amount DECIMAL(20,2) COMMENT '交易额 (对应Oracle jy_amount)',
            create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY uk_stock_date (stock_code, trade_date),
            KEY idx_stock_code (stock_code),
            KEY idx_trade_date (trade_date),
            KEY idx_week_num (week_num)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票周线数据 (对应Oracle GP_JY_W)'
        """
        self.db.execute_update(sql)
    
    def create_avg_trade_table(self):
        """创建平均交易量表 - 严格对应Oracle GP_AVG_WTRADE表结构"""
        sql = """
        CREATE TABLE IF NOT EXISTS stock_avg_trade (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            stock_code VARCHAR(10) NOT NULL COMMENT '股票代码 (对应Oracle gp_num)',
            trade_date DATE NOT NULL COMMENT '交易日期 (对应Oracle gp_jy_date)',
            avg_days INT NOT NULL COMMENT '平均天数 (对应Oracle avg_days)',
            avg_qty DECIMAL(15,2) COMMENT '平均交易量 (对应Oracle avg_qty)',
            avg_amount DECIMAL(20,2) COMMENT '平均交易额 (对应Oracle avg_amount)',
            create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY uk_stock_date_days (stock_code, trade_date, avg_days),
            KEY idx_stock_code (stock_code),
            KEY idx_trade_date (trade_date),
            KEY idx_avg_days (avg_days)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票平均交易量数据 (对应Oracle GP_AVG_WTRADE)'
        """
        self.db.execute_update(sql)
    
    def create_moving_average_table(self):
        """创建移动平均线表 - 严格对应Oracle GP_AVG_JYW表结构"""
        sql = """
        CREATE TABLE IF NOT EXISTS stock_moving_average (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            stock_code VARCHAR(10) NOT NULL COMMENT '股票代码 (对应Oracle gp_num)',
            week_num VARCHAR(20) NOT NULL COMMENT '周编号 (对应Oracle gp_week_num)',
            trade_date DATE NOT NULL COMMENT '交易日期 (对应Oracle gp_jy_date)',
            avg_months INT NOT NULL COMMENT '平均月数/周期数1-20 (对应Oracle avg_months)',
            avg_value DECIMAL(10,4) NOT NULL COMMENT '移动平均价格 (对应Oracle avg_value)',
            create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 (对应Oracle create_date)',
            
            -- Oracle索引对应: GP_AVG_JYW_U1 on (GP_NUM, GP_JY_DATE, AVG_MONTHS)
            UNIQUE KEY uk_stock_date_months (stock_code, trade_date, avg_months),
            
            -- 其他优化索引
            KEY idx_stock_code (stock_code),
            KEY idx_trade_date (trade_date),
            KEY idx_avg_months (avg_months),
            KEY idx_week_num (week_num)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票移动平均线数据 (对应Oracle GP_AVG_JYW)'
        """
        self.db.execute_update(sql)
    
    def create_volatility_table(self):
        """创建波动率表 - 对应Oracle CALCU_FLUCT"""
        sql = """
        CREATE TABLE IF NOT EXISTS stock_volatility (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            stock_code VARCHAR(10) NOT NULL,
            trade_date DATE NOT NULL,
            daily_tr DECIMAL(10,4) COMMENT '日真实波幅(True Range)',
            atr_20 DECIMAL(10,4) COMMENT '20日平均真实波幅',
            create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY uk_stock_date (stock_code, trade_date),
            KEY idx_stock_code (stock_code),
            KEY idx_trade_date (trade_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票波动率数据'
        """
        self.db.execute_update(sql)
    
    def create_analysis_result_table(self):
        """创建分析结果表"""
        sql = """
        CREATE TABLE IF NOT EXISTS stock_analysis_result (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            batch_id BIGINT NOT NULL,
            tactics_code VARCHAR(20),
            stock_code VARCHAR(10) NOT NULL,
            stock_name VARCHAR(100),
            u_left_date DATE,
            u_left_price DECIMAL(10,4),
            u_right_date DATE,
            u_right_price DECIMAL(10,4),
            v_right_date DATE,
            v_right_price DECIMAL(10,4),
            u_lowest_price DECIMAL(10,4),
            u_lowest_date DATE,
            v_lowest_price DECIMAL(10,4),
            v_lowest_date DATE,
            all_turnover_rate DECIMAL(10,6),
            avg_turnover_rate DECIMAL(10,6),
            success_date DATE,
            success_price DECIMAL(10,4),
            lower_date DATE,
            lower_price DECIMAL(10,4),
            u_low1_price DECIMAL(10,4),
            u_low1_date DATE,
            u_low2_price DECIMAL(10,4),
            u_low2_date DATE,
            ma_num DECIMAL(10,2),
            is_breakthrough TINYINT DEFAULT 0,
            breakthrough_price DECIMAL(10,4),
            breakthrough_date DATE,
            create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
            KEY idx_batch_id (batch_id),
            KEY idx_stock_code (stock_code),
            KEY idx_tactics_code (tactics_code),
            KEY idx_u_left_date (u_left_date),
            KEY idx_v_right_date (v_right_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票技术分析结果'
        """
        self.db.execute_update(sql)
    
    def insert_weekly_data(self, weekly_data: List[Dict[str, Any]]):
        """插入周线数据 - 严格对应Oracle GP_JY_W表结构"""
        if not weekly_data:
            return 0
        
        # Oracle GP_JY_W表字段对应：
        # gp_num → stock_code, jy_date → trade_date, week_num → week_num
        # open_price → open_price, high_price → high_price, close_price → close_price, low_price → low_price
        # jy_quantity → jy_quantity, jy_amount → jy_amount
        sql = """
        INSERT INTO stock_weekly_data 
        (stock_code, trade_date, week_num, open_price, high_price, close_price, low_price, jy_quantity, jy_amount)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        open_price=VALUES(open_price), high_price=VALUES(high_price),
        close_price=VALUES(close_price), low_price=VALUES(low_price),
        jy_quantity=VALUES(jy_quantity), jy_amount=VALUES(jy_amount)
        """
        
        try:
            params_list = []
            for data in weekly_data:
                params_list.append((
                    data['stock_code'], data['trade_date'], data['week_num'],
                    data['open_price'], data['high_price'], data['close_price'],
                    data['low_price'], data['jy_quantity'], data['jy_amount']
                ))
            
            result = self.db.execute_batch(sql, params_list, batch_size=2000)
            if result == 0 and len(params_list) > 0:
                print(f"警告: 执行周线数据批量插入，预期插入{len(params_list)}条，实际返回{result}")
                print(f"SQL: {sql}")
                print(f"样本数据: {params_list[0] if params_list else 'None'}")
            
            return result
            
        except Exception as e:
            print(f"插入周线数据失败: {e}")
            print(f"SQL: {sql}")
            print(f"数据样本: {weekly_data[0] if weekly_data else 'None'}")
            raise
    
    def insert_avg_trade_data(self, avg_data: List[Dict[str, Any]]):
        """插入平均交易量数据 - 优化批量插入"""
        if not avg_data:
            return 0
        
        sql = """
        INSERT INTO stock_avg_trade 
        (stock_code, trade_date, avg_days, avg_qty, avg_amount)
        VALUES (%s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        avg_qty=VALUES(avg_qty), avg_amount=VALUES(avg_amount)
        """
        
        try:
            params_list = []
            for data in avg_data:
                params_list.append((
                    data['stock_code'], data['trade_date'], 
                    data['avg_days'], data['avg_qty'], data['avg_amount']
                ))
            
            result = self.db.execute_batch(sql, params_list, batch_size=2000)
            if result == 0 and len(params_list) > 0:
                print(f"警告: 执行批量插入，预期插入{len(params_list)}条，实际返回{result}")
                print(f"SQL: {sql}")
                print(f"样本数据: {params_list[0] if params_list else 'None'}")
            
            return result
            
        except Exception as e:
            print(f"插入平均交易量数据失败: {e}")
            print(f"SQL: {sql}")
            print(f"数据样本: {avg_data[0] if avg_data else 'None'}")
            raise
    
    def insert_moving_average_data(self, ma_data: List[Dict[str, Any]]):
        """插入移动平均线数据 - 对应Oracle GP_AVG_JYW表"""
        if not ma_data:
            return 0
        
        # Oracle GP_AVG_JYW表字段对应：
        # stock_code → gp_num, week_num → gp_week_num, trade_date → gp_jy_date
        # avg_months → avg_months, avg_value → avg_value
        sql = """
        INSERT INTO stock_moving_average 
        (stock_code, week_num, trade_date, avg_months, avg_value)
        VALUES (%s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        avg_value=VALUES(avg_value), week_num=VALUES(week_num)
        """
        
        try:
            params_list = []
            for data in ma_data:
                params_list.append((
                    data['stock_code'], 
                    data.get('week_num'),
                    data['trade_date'], 
                    data['avg_months'],  # Oracle字段名：avg_months
                    data['avg_value']
                ))
            
            result = self.db.execute_batch(sql, params_list, batch_size=2000)
            if result == 0 and len(params_list) > 0:
                print(f"警告: 执行移动平均线数据批量插入，预期插入{len(params_list)}条，实际返回{result}")
                print(f"SQL: {sql}")
                print(f"样本数据: {params_list[0] if params_list else 'None'}")
            
            return result
            
        except Exception as e:
            print(f"插入移动平均线数据失败: {e}")
            print(f"SQL: {sql}")
            print(f"数据样本: {ma_data[0] if ma_data else 'None'}")
            raise
    
    def insert_volatility_data(self, volatility_data: List[Dict[str, Any]]):
        """插入波动率数据 - 对应Oracle CALCU_FLUCT"""
        if not volatility_data:
            return 0
        
        sql = """
        INSERT INTO stock_volatility 
        (stock_code, trade_date, daily_tr, atr_20)
        VALUES (%s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        daily_tr=VALUES(daily_tr), atr_20=VALUES(atr_20)
        """
        
        try:
            params_list = []
            for data in volatility_data:
                params_list.append((
                    data['stock_code'], data['trade_date'], 
                    data.get('daily_tr'), data.get('atr_20')
                ))
            
            result = self.db.execute_batch(sql, params_list, batch_size=2000)
            if result == 0 and len(params_list) > 0:
                print(f"警告: 执行波动率数据批量插入，预期插入{len(params_list)}条，实际返回{result}")
                print(f"SQL: {sql}")
                print(f"样本数据: {params_list[0] if params_list else 'None'}")
            
            return result
            
        except Exception as e:
            print(f"插入波动率数据失败: {e}")
            print(f"SQL: {sql}")
            print(f"数据样本: {volatility_data[0] if volatility_data else 'None'}")
            raise
    
    def insert_analysis_result(self, result_data: Dict[str, Any]) -> int:
        """插入分析结果 - 支持重试，包含所有补全字段"""
        sql = """
        INSERT INTO stock_analysis_result 
        (batch_id, tactics_code, stock_code, stock_name, u_left_date, u_left_price,
         u_right_date, u_right_price, v_right_date, v_right_price, u_lowest_price,
         u_lowest_date, v_lowest_price, v_lowest_date, all_turnover_rate, avg_turnover_rate,
         success_date, success_price, lower_date, lower_price, u_low1_price, u_low1_date,
         u_low2_price, u_low2_date, ma_num, is_breakthrough, breakthrough_price, breakthrough_date)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        params = (
            result_data['batch_id'], result_data.get('tactics_code', 'UV_PATTERN'),
            result_data['stock_code'], result_data['stock_name'],
            result_data.get('u_left_date'), result_data.get('u_left_price'),
            result_data.get('u_right_date'), result_data.get('u_right_price'),
            result_data.get('v_right_date'), result_data.get('v_right_price'),
            result_data.get('u_lowest_price'), result_data.get('u_lowest_date'),
            result_data.get('v_lowest_price'), result_data.get('v_lowest_date'),
            result_data.get('all_turnover_rate'), result_data.get('avg_turnover_rate'),
            result_data.get('success_date'), result_data.get('success_price'),
            result_data.get('lower_date'), result_data.get('lower_price'),
            result_data.get('u_low1_price'), result_data.get('u_low1_date'),
            result_data.get('u_low2_price'), result_data.get('u_low2_date'),
            result_data.get('ma_num'), result_data.get('is_breakthrough', 0), 
            result_data.get('breakthrough_price'), result_data.get('breakthrough_date')
        )
        
        return self.db.execute_update(sql, params)
    
    def get_next_batch_id(self) -> int:
        """获取下一个批次ID"""
        sql = "SELECT IFNULL(MAX(batch_id), 0) + 1 as next_batch_id FROM stock_analysis_result"
        result = self.db.execute_query(sql)
        return result[0]['next_batch_id'] if result else 1
    
    def save_weekly_data(self, stock_code: str, weekly_data: List[Dict[str, Any]]):
        """保存周线数据"""
        if not weekly_data:
            return
        
        # 为数据添加股票代码
        for data in weekly_data:
            data['stock_code'] = stock_code
        
        self.insert_weekly_data(weekly_data)
    
    def get_weekly_data(self, stock_code: str) -> List[Dict[str, Any]]:
        """获取股票周线数据 - 对应Oracle GP_JY_W表查询
        注意：Oracle版本有时间过滤条件 jy_date >= '2013-01-01'
        """
        sql = """
        SELECT trade_date, open_price, high_price, close_price, low_price, jy_quantity, jy_amount
        FROM stock_weekly_data
        WHERE stock_code = %s
          AND trade_date >= '2013-01-01'
        ORDER BY trade_date
        """
        return self.db.execute_query(sql, (stock_code,))
    
    def save_avg_trade_data(self, stock_code: str, avg_data: List[Dict[str, Any]]):
        """保存平均交易量数据"""
        if not avg_data:
            return
        
        try:
            # 统一字段名称并验证数据完整性
            for data in avg_data:
                if 'stock_code' not in data:
                    data['stock_code'] = stock_code
                
                # 验证必要字段
                required_fields = ['stock_code', 'trade_date', 'avg_days', 'avg_qty', 'avg_amount']
                missing_fields = [field for field in required_fields if field not in data]
                if missing_fields:
                    raise ValueError(f"数据缺少必要字段: {missing_fields}, 数据: {data}")
            
            # 执行插入
            result = self.insert_avg_trade_data(avg_data)
            if result == 0:
                print(f"警告: 股票 {stock_code} 平均交易量数据插入返回0行")
            
        except Exception as e:
            print(f"保存股票 {stock_code} 平均交易量数据失败: {e}")
            print(f"数据样本: {avg_data[:2] if len(avg_data) > 0 else 'Empty'}")
            raise
    
    def get_avg_trade_data(self, stock_code: str) -> List[Dict[str, Any]]:
        """获取股票平均交易量数据"""
        sql = """
        SELECT trade_date, avg_days, avg_qty, avg_amount
        FROM stock_avg_trade
        WHERE stock_code = %s
        ORDER BY trade_date, avg_days
        """
        return self.db.execute_query(sql, (stock_code,))
    
    def get_moving_average_data(self, stock_code: str, start_date=None, end_date=None) -> List[Dict[str, Any]]:
        """
        获取移动平均线数据 - 用于CHECK_MA逻辑
        对应Oracle GP_AVG_JYW表查询
        
        Oracle字段对应：
        - gp_num → stock_code
        - gp_jy_date → trade_date  
        - avg_months → avg_months
        - avg_value → avg_value
        """
        try:
            sql = """
            SELECT 
                stock_code,
                trade_date,
                avg_months,
                avg_value
            FROM stock_moving_average
            WHERE stock_code = %s
            """
            params = [stock_code]
            
            if start_date:
                sql += " AND trade_date >= %s"
                params.append(start_date)
            
            if end_date:
                sql += " AND trade_date <= %s"
                params.append(end_date)
            
            sql += " ORDER BY trade_date, avg_months"
            
            with self.db.get_connection() as conn:
                with conn.cursor(pymysql.cursors.DictCursor) as cursor:
                    cursor.execute(sql, params)
                    return cursor.fetchall()
                    
        except Exception as e:
            print(f"获取移动平均线数据失败 {stock_code}: {e}")
            return []
    
    def get_volatility_data(self, stock_code: str) -> List[Dict[str, Any]]:
        """获取股票波动率数据"""
        sql = """
        SELECT trade_date, daily_tr, atr_20
        FROM stock_volatility
        WHERE stock_code = %s
        ORDER BY trade_date
        """
        return self.db.execute_query(sql, (stock_code,))

    def get_analysis_results(self, batch_id: int) -> List[Dict[str, Any]]:
        """获取指定批次的分析结果"""
        sql = """
        SELECT stock_code, stock_name, u_left_date, u_left_price,
               u_right_date, u_right_price, v_right_date, v_right_price,
               u_lowest_price, u_lowest_date, v_lowest_price, v_lowest_date,
               all_turnover_rate, avg_turnover_rate, success_date, success_price,
               lower_date, lower_price, u_low1_price, u_low1_date,
               u_low2_price, u_low2_date, ma_num, is_breakthrough,
               breakthrough_price, breakthrough_date
        FROM stock_analysis_result
        WHERE batch_id = %s
        ORDER BY id
        """
        return self.db.execute_query(sql, (batch_id,))

    def clear_analysis_results(self):
        """清空分析结果表，只保留最新数据"""
        sql = "TRUNCATE TABLE stock_analysis_result"
        return self.db.execute_update(sql)

    def clear_avg_trade_data(self):
        """清空平均交易量数据表"""
        sql = "TRUNCATE TABLE stock_avg_trade"
        return self.db.execute_update(sql)