#!/usr/bin/env python3
"""
趋势分析系统安装设置脚本
"""

import sys
import os
import logging
from datetime import datetime

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'trend_setup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
            logging.StreamHandler()
        ]
    )

def check_python_version():
    """检查Python版本"""
    logger = logging.getLogger(__name__)
    
    if sys.version_info < (3, 6):
        logger.error("需要Python 3.6或更高版本")
        return False
    
    logger.info(f"Python版本检查通过: {sys.version}")
    return True

def check_dependencies():
    """检查依赖包"""
    logger = logging.getLogger(__name__)
    
    required_packages = [
        'pymysql',
        'logging',
        'concurrent.futures',
        'datetime',
        'typing',
        'dataclasses',
        'math'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            logger.warning(f"✗ {package} - 缺失")
    
    if missing_packages:
        logger.error(f"缺失依赖包: {missing_packages}")
        logger.info("请运行: pip install " + " ".join(missing_packages))
        return False
    
    logger.info("所有依赖包检查通过")
    return True

def check_sw_py_integration():
    """检查sw_py集成"""
    logger = logging.getLogger(__name__)
    
    try:
        # 检查sw_py路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        sw_py_path = os.path.join(current_dir, '..', '..', 'sw_py')
        
        if os.path.exists(sw_py_path):
            logger.info(f"✓ sw_py路径存在: {sw_py_path}")
            
            # 检查关键文件
            key_files = [
                'models/database.py',
                'config/analysis_config.py'
            ]
            
            for file_path in key_files:
                full_path = os.path.join(sw_py_path, file_path)
                if os.path.exists(full_path):
                    logger.info(f"✓ {file_path}")
                else:
                    logger.warning(f"✗ {file_path} - 不存在")
            
            return True
        else:
            logger.warning(f"sw_py路径不存在: {sw_py_path}")
            logger.info("系统将使用独立模式运行")
            return True
            
    except Exception as e:
        logger.error(f"sw_py集成检查失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    logger = logging.getLogger(__name__)
    
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 创建日志目录
        log_dir = os.path.join(current_dir, 'logs')
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
            logger.info(f"创建日志目录: {log_dir}")
        
        # 创建数据目录
        data_dir = os.path.join(current_dir, 'data')
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
            logger.info(f"创建数据目录: {data_dir}")
        
        return True
    except Exception as e:
        logger.error(f"目录创建失败: {e}")
        return False

def test_system():
    """测试系统"""
    logger = logging.getLogger(__name__)
    
    try:
        # 运行测试脚本
        current_dir = os.path.dirname(os.path.abspath(__file__))
        test_script = os.path.join(current_dir, 'test_trend.py')
        
        if os.path.exists(test_script):
            logger.info("运行系统测试...")
            
            # 这里可以调用测试脚本
            # 为了简化，我们只是导入测试模块
            sys.path.append(current_dir)
            
            try:
                import test_trend
                logger.info("✓ 测试模块导入成功")
                return True
            except Exception as e:
                logger.error(f"测试模块导入失败: {e}")
                return False
        else:
            logger.warning("测试脚本不存在")
            return True
            
    except Exception as e:
        logger.error(f"系统测试失败: {e}")
        return False

def print_usage_instructions():
    """打印使用说明"""
    logger = logging.getLogger(__name__)
    
    logger.info("\n" + "=" * 60)
    logger.info("趋势分析系统安装完成")
    logger.info("=" * 60)
    
    logger.info("\n使用方法:")
    logger.info("1. 基本运行:")
    logger.info("   python pkg/trend/run_trend_analysis.py")
    
    logger.info("\n2. 调试模式:")
    logger.info("   python pkg/trend/run_trend_analysis.py --debug")
    
    logger.info("\n3. 测试单只股票:")
    logger.info("   python pkg/trend/run_trend_analysis.py --test-stock 601002")
    
    logger.info("\n4. 运行系统测试:")
    logger.info("   python pkg/trend/test_trend.py")
    
    logger.info("\n5. 查看帮助:")
    logger.info("   python pkg/trend/run_trend_analysis.py --help")
    
    logger.info("\n配置文件:")
    logger.info("- pkg/trend/config.py - 主要配置参数")
    logger.info("- 可以修改时间框架、线程数等参数")
    
    logger.info("\n日志文件:")
    logger.info("- 运行日志会保存在当前目录")
    logger.info("- 文件名格式: trend_analysis_YYYYMMDD_HHMMSS.log")

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("开始趋势分析系统安装设置")
    
    steps = [
        ("Python版本检查", check_python_version),
        ("依赖包检查", check_dependencies),
        ("sw_py集成检查", check_sw_py_integration),
        ("目录创建", create_directories),
        ("系统测试", test_system),
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        logger.info(f"\n执行: {step_name}")
        try:
            if step_func():
                success_count += 1
                logger.info(f"✓ {step_name} 完成")
            else:
                logger.error(f"✗ {step_name} 失败")
        except Exception as e:
            logger.error(f"✗ {step_name} 异常: {e}")
    
    if success_count == len(steps):
        logger.info("\n🎉 安装设置完成！")
        print_usage_instructions()
        return True
    else:
        logger.error(f"\n⚠️  安装设置部分失败 ({success_count}/{len(steps)})")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n安装失败: {e}")
        sys.exit(1)
