"""
趋势分析数据模型
对应Oracle gp_Trend_pkg的数据库操作
"""

import sys
import os
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date
import logging
import math

# 添加sw_py路径以使用现有的数据库连接
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'sw_py'))

try:
    from models.database import DatabaseManager
except ImportError:
    try:
        # 尝试从sw_py导入
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'sw_py'))
        from models.database import DatabaseManager
    except ImportError:
        # 如果无法导入sw_py模块，使用本地数据库配置
        DatabaseManager = None
        import pymysql

class TrendDatabaseManager:
    """趋势分析数据库管理器"""
    
    def __init__(self, db_manager: Optional[DatabaseManager] = None):
        """初始化数据库管理器"""
        if db_manager:
            self.db = db_manager
        else:
            # 使用sw_py的数据库管理器
            self.db = DatabaseManager()
        
        self.logger = logging.getLogger(__name__)
    
    def create_trend_result_table(self):
        """创建趋势分析结果表 - 对应Oracle GP_TREND_RESULT"""
        sql = """
        CREATE TABLE IF NOT EXISTS gp_trend_result (
            record_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '序号',
            gp_num VARCHAR(10) NOT NULL COMMENT '股票代码',
            dates_type INT NOT NULL COMMENT '时间框架(200天/400天/600天)',
            down_a0 DATE COMMENT 'A0日期(下降点1)',
            down_a2 DATE COMMENT 'A2日期(下降点2)',
            point_a DECIMAL(15,8) COMMENT '系数：A',
            point_b DECIMAL(15,8) COMMENT '系数：B',
            top_a0 DECIMAL(10,4) COMMENT 'A0最高价',
            top_a2 DECIMAL(10,4) COMMENT 'A2最高价',
            tp_b0 DATE COMMENT 'B0日期(突破点)',
            up_a1 DATE COMMENT 'A1日期(上升点1)',
            up_b2 DATE COMMENT 'B2日期(上升点2)',
            point_c DECIMAL(15,8) COMMENT '系数：C',
            point_d DECIMAL(15,8) COMMENT '系数：D',
            low_a1 DECIMAL(10,4) COMMENT 'A1最低价',
            low_b2 DECIMAL(10,4) COMMENT 'B2最低价',
            date_b1 DATE COMMENT 'B1日期',
            attribute1 VARCHAR(100) COMMENT '类型(自然数/自然对数)',
            attribute2 VARCHAR(100) COMMENT '标志',
            attribute3 VARCHAR(100),
            attribute4 VARCHAR(100),
            attribute5 VARCHAR(100),
            attribute6 VARCHAR(100),
            attribute7 VARCHAR(100),
            attribute8 VARCHAR(100),
            attribute9 VARCHAR(100),
            attribute10 VARCHAR(100),
            attribute11 VARCHAR(100),
            attribute12 VARCHAR(100),
            attribute13 VARCHAR(100),
            attribute14 VARCHAR(100),
            attribute15 VARCHAR(100),
            creation_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            
            UNIQUE KEY uk_gp_dates_attr (gp_num, dates_type, attribute1),
            KEY idx_gp_num (gp_num),
            KEY idx_dates_type (dates_type),
            KEY idx_creation_date (creation_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='趋势分析结果表'
        """
        
        self.db.execute_query(sql)
        self.logger.info("趋势分析结果表创建完成")
    
    def create_temp_table(self):
        """创建临时表 - 对应Oracle GP_JY_D_TMP"""
        sql = """
        CREATE TEMPORARY TABLE IF NOT EXISTS gp_jy_d_tmp (
            row_num INT NOT NULL,
            record_id BIGINT,
            gp_num VARCHAR(10),
            jy_date DATE,
            open_price DECIMAL(10,4),
            high_price DECIMAL(10,4),
            close_price DECIMAL(10,4),
            low_price DECIMAL(10,4),
            jy_quantity BIGINT,
            jy_amount DECIMAL(15,4),
            adj_close DECIMAL(10,4),
            creation_date DATETIME,
            
            PRIMARY KEY (row_num),
            KEY idx_gp_num (gp_num),
            KEY idx_jy_date (jy_date),
            KEY idx_high_price (high_price),
            KEY idx_close_price (close_price)
        ) ENGINE=MEMORY
        """
        
        self.db.execute_query(sql)
        self.logger.debug("临时表创建完成")
    
    def truncate_temp_table(self):
        """清空临时表 - 对应Oracle TRUNCATE TABLE GP_JY_D_TMP"""
        sql = "TRUNCATE TABLE gp_jy_d_tmp"
        self.db.execute_query(sql)
    
    def load_stock_data_to_temp(self, stock_code: str):
        """加载股票数据到临时表 - 对应Oracle插入GP_JY_D_TMP的逻辑"""
        # 先清空临时表
        self.truncate_temp_table()
        
        # 从stock_daily_data表加载数据，按日期排序并添加行号
        sql = """
        INSERT INTO gp_jy_d_tmp 
        (row_num, record_id, gp_num, jy_date, open_price, high_price, 
         close_price, low_price, jy_quantity, jy_amount, adj_close, creation_date)
        SELECT 
            ROW_NUMBER() OVER (ORDER BY trade_date) as row_num,
            id as record_id,
            stock_code as gp_num,
            trade_date as jy_date,
            open_price,
            high_price,
            close_price,
            low_price,
            volume as jy_quantity,
            amount as jy_amount,
            adj_close,
            create_time as creation_date
        FROM stock_daily_data 
        WHERE stock_code = %s 
        ORDER BY trade_date
        """
        
        result = self.db.execute_query(sql, (stock_code,))
        return result
    
    def get_max_row_num(self) -> int:
        """获取临时表最大行号 - 对应Oracle select max(row_num)"""
        sql = "SELECT MAX(row_num) as max_num FROM gp_jy_d_tmp"
        result = self.db.execute_query(sql)
        return result[0]['max_num'] if result and result[0]['max_num'] else 0
    
    def get_stock_list(self) -> List[Dict[str, Any]]:
        """获取股票列表 - 对应Oracle查询gp_info_t"""
        sql = """
        SELECT DISTINCT stock_code as gp_num
        FROM stock_list 
        WHERE exchange IN ('sh', 'sz')
        AND EXISTS (
            SELECT 1 FROM stock_daily_data 
            WHERE stock_code = stock_list.stock_code
        )
        ORDER BY stock_code
        """
        
        return self.db.execute_query(sql)
    
    def truncate_result_table(self):
        """清空结果表 - 对应Oracle truncate table gp_trend_result"""
        sql = "TRUNCATE TABLE gp_trend_result"
        self.db.execute_query(sql)
        self.logger.info("趋势分析结果表已清空")
    
    def insert_trend_result(self, result_data: Dict[str, Any]) -> bool:
        """插入趋势分析结果 - 对应Oracle insert into gp_trend_result"""
        sql = """
        INSERT INTO gp_trend_result 
        (gp_num, dates_type, down_a0, down_a2, point_a, point_b, top_a0, top_a2,
         tp_b0, up_a1, up_b2, point_c, point_d, low_a1, low_b2, date_b1,
         attribute1, attribute2)
        VALUES 
        (%(gp_num)s, %(dates_type)s, %(down_a0)s, %(down_a2)s, %(point_a)s, %(point_b)s,
         %(top_a0)s, %(top_a2)s, %(tp_b0)s, %(up_a1)s, %(up_b2)s, %(point_c)s,
         %(point_d)s, %(low_a1)s, %(low_b2)s, %(date_b1)s, %(attribute1)s, %(attribute2)s)
        """
        
        try:
            self.db.execute_query(sql, result_data)
            return True
        except Exception as e:
            self.logger.error(f"插入趋势分析结果失败: {e}")
            return False
    
    def calculate_avg_volume(self, stock_code: str, trade_date: date, days: int) -> float:
        """计算平均交易量 - 对应Oracle CALCUL_AVG_DQTY函数"""
        sql = """
        SELECT AVG(COALESCE(jy_quantity, 0)) as avg_qty
        FROM gp_jy_d_tmp 
        WHERE gp_num = %s 
        AND jy_date < %s 
        AND row_num BETWEEN (
            SELECT row_num FROM gp_jy_d_tmp 
            WHERE gp_num = %s AND jy_date = %s
        ) - %s - 1 AND (
            SELECT row_num FROM gp_jy_d_tmp 
            WHERE gp_num = %s AND jy_date = %s
        ) - 1
        """
        
        try:
            result = self.db.execute_query(sql, (stock_code, trade_date, stock_code, trade_date, days, stock_code, trade_date))
            return float(result[0]['avg_qty']) if result and result[0]['avg_qty'] else 0.0
        except Exception as e:
            self.logger.error(f"计算平均交易量失败: {e}")
            return 99999999999999.0  # Oracle异常处理返回值
