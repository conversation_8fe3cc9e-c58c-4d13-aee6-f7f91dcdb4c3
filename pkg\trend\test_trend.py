#!/usr/bin/env python3
"""
趋势分析系统测试脚本
用于验证系统功能是否正常
"""

import sys
import os
import logging
from datetime import datetime

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.join(current_dir, '..', '..'))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    try:
        from pkg.trend.config import TREND_CONFIG, DEBUG_CONFIG
        print("✓ 配置模块导入成功")
        
        from pkg.trend.models import TrendDatabaseManager
        print("✓ 数据库模型导入成功")
        
        from pkg.trend.trend_analyzer import TrendAnalyzer, TrendLineValidator
        print("✓ 趋势分析器导入成功")
        
        from pkg.trend.main import TrendAnalysisMain
        print("✓ 主程序导入成功")
        
        return True
    except Exception as e:
        print(f"✗ 模块导入失败: {e}")
        return False

def test_config():
    """测试配置"""
    print("\n测试配置...")
    try:
        from pkg.trend.config import TREND_CONFIG, DEBUG_CONFIG, THREADING_CONFIG
        
        # 检查必要的配置项
        assert 'time_frames' in TREND_CONFIG
        assert 'trend_validation' in TREND_CONFIG
        assert 'breakout_confirmation' in TREND_CONFIG
        assert 'max_workers' in THREADING_CONFIG
        
        print("✓ 配置验证成功")
        print(f"  - 时间框架: {TREND_CONFIG['time_frames']}")
        print(f"  - 最大线程数: {THREADING_CONFIG['max_workers']}")
        
        return True
    except Exception as e:
        print(f"✗ 配置验证失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n测试数据库连接...")
    try:
        from pkg.trend.models import TrendDatabaseManager
        
        # 尝试创建数据库管理器
        db_manager = TrendDatabaseManager()
        print("✓ 数据库管理器创建成功")
        
        # 尝试创建表（如果数据库可用）
        try:
            db_manager.create_trend_result_table()
            print("✓ 趋势结果表创建/验证成功")
        except Exception as e:
            print(f"⚠ 趋势结果表创建失败（可能是数据库连接问题）: {e}")
        
        return True
    except Exception as e:
        print(f"✗ 数据库连接测试失败: {e}")
        return False

def test_analyzer_creation():
    """测试分析器创建"""
    print("\n测试分析器创建...")
    try:
        from pkg.trend.models import TrendDatabaseManager
        from pkg.trend.trend_analyzer import TrendAnalyzer, TrendLineValidator
        
        # 创建数据库管理器
        db_manager = TrendDatabaseManager()
        
        # 创建趋势线验证器
        validator = TrendLineValidator(db_manager)
        print("✓ 趋势线验证器创建成功")
        
        # 创建趋势分析器
        analyzer = TrendAnalyzer(db_manager)
        print("✓ 趋势分析器创建成功")
        
        return True
    except Exception as e:
        print(f"✗ 分析器创建失败: {e}")
        return False

def test_main_program():
    """测试主程序"""
    print("\n测试主程序...")
    try:
        from pkg.trend.main import TrendAnalysisMain
        
        # 创建主程序实例
        main_program = TrendAnalysisMain()
        print("✓ 主程序实例创建成功")
        
        # 测试日志设置
        main_program.setup_logging()
        print("✓ 日志设置成功")
        
        return True
    except Exception as e:
        print(f"✗ 主程序测试失败: {e}")
        return False

def test_debug_mode():
    """测试调试模式"""
    print("\n测试调试模式...")
    try:
        from pkg.trend.config import DEBUG_CONFIG
        
        # 启用调试模式
        original_debug = DEBUG_CONFIG['enable_detailed_logging']
        original_test_stock = DEBUG_CONFIG['test_single_stock']
        
        DEBUG_CONFIG['enable_detailed_logging'] = True
        DEBUG_CONFIG['test_single_stock'] = '601002'
        
        print("✓ 调试模式设置成功")
        print(f"  - 详细日志: {DEBUG_CONFIG['enable_detailed_logging']}")
        print(f"  - 测试股票: {DEBUG_CONFIG['test_single_stock']}")
        
        # 恢复原始设置
        DEBUG_CONFIG['enable_detailed_logging'] = original_debug
        DEBUG_CONFIG['test_single_stock'] = original_test_stock
        
        return True
    except Exception as e:
        print(f"✗ 调试模式测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("趋势分析系统测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("配置验证", test_config),
        ("数据库连接", test_database_connection),
        ("分析器创建", test_analyzer_creation),
        ("主程序", test_main_program),
        ("调试模式", test_debug_mode),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关配置。")
        return False

def main():
    """主函数"""
    try:
        success = run_all_tests()
        
        if success:
            print("\n建议的下一步操作：")
            print("1. 确保数据库连接正常")
            print("2. 检查stock_daily_data表是否有数据")
            print("3. 运行测试分析：")
            print("   python pkg/trend/run_trend_analysis.py --test-stock 601002 --debug")
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
